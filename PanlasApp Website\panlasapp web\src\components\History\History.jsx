import React, { useEffect, useState } from "react";
import Layout from "../Layout/Layout";
import apiService from "../../services/apiService.js";
import { FaClock, FaTimes, FaHeart, FaRegHeart, FaFire, FaChevronRight } from "react-icons/fa";
const RECENTLY_VIEWED_KEY = "recentlyViewedMeals";

function getDietaryBadges(meal) {
  const BADGE_MAP = [
    { key: 'Vegan', label: 'Vegan', color: '#4CAF50', icon: '🌱' },
    { key: 'Vegetarian', label: 'Vegetarian', color: '#8BC34A', icon: '🥬' },
    { key: 'Flexitarian', label: 'Flexitarian', color: '#A3C9A8', icon: '🤝' },
    { key: 'Dairy-Free', label: 'Dairy-Free', color: '#00BCD4', icon: '🥛🚫' },
    { key: 'Egg-Free', label: 'Egg-Free', color: '#FFEB3B', icon: '🥚🚫' },
    { key: 'Gluten-Free', label: 'Gluten-Free', color: '#FF9800', icon: '🌾' },
    { key: 'Soy-Free', label: 'Soy-Free', color: '#9E9E9E', icon: '🌱🚫' },
    { key: 'Nut-Free', label: 'Nut-Free', color: '#795548', icon: '🥜🚫' },
    { key: 'Low-Carb', label: 'Low-Carb', color: '#9C27B0', icon: '🥩' },
    { key: 'Low-Sugar', label: 'Low-Sugar', color: '#607D8B', icon: '🍬⬇️' },
    { key: 'Sugar-Free', label: 'Sugar-Free', color: '#607D8B', icon: '🍬🚫' },
    { key: 'Low-Fat', label: 'Low-Fat', color: '#03A9F4', icon: '🥗' },
    { key: 'Low-Sodium', label: 'Low-Sodium', color: '#B0BEC5', icon: '🧂⬇️' },
    { key: 'Organic', label: 'Organic', color: '#388E3C', icon: '🍃' },
    { key: 'Halal', label: 'Halal', color: '#2196F3', icon: '☪️' },
    { key: 'High-Protein', label: 'High-Protein', color: '#E91E63', icon: '💪' },
    { key: 'Pescatarian', label: 'Pescatarian', color: '#00B8D4', icon: '🐟' },
    { key: 'Keto', label: 'Keto', color: '#FFB300', icon: '🥓' },
    { key: 'Plant-Based', label: 'Plant-Based', color: '#43A047', icon: '🌿' },
    { key: 'Kosher', label: 'Kosher', color: '#3F51B5', icon: '✡️' },
    { key: 'Climatarian', label: 'Climatarian', color: '#689F38', icon: '🌎' },
    { key: 'Raw Food', label: 'Raw Food', color: '#AED581', icon: '🥗' },
    { key: 'Mediterranean', label: 'Mediterranean', color: '#00ACC1', icon: '🌊' },
    { key: 'Paleo', label: 'Paleo', color: '#A1887F', icon: '🍖' },
    { key: 'Kangatarian', label: 'Kangatarian', color: '#D84315', icon: '🦘' },
    { key: 'Pollotarian', label: 'Pollotarian', color: '#FBC02D', icon: '🍗' },
  ];
  const tags = meal.dietaryTags || [];
  return BADGE_MAP.filter(badge =>
    tags.some(tag => tag.toLowerCase() === badge.key.toLowerCase())
  );
}
// Convert price range to peso signs
const getPesoSigns = (priceRange) => {
  switch (priceRange) {
    case "Low": return "₱";
    case "Mid": return "₱₱";
    case "High": return "₱₱₱";
    default: return "₱";
  }
};

const History = () => {
  const [recentMeals, setRecentMeals] = useState([]);
  const [mealsFromSavedPlans, setMealsFromSavedPlans] = useState([]);
  const [loading, setLoading] = useState(true);
  const [refreshingRecent, setRefreshingRecent] = useState(false);
  const [refreshingSaved, setRefreshingSaved] = useState(false);
  const [clearingRecent, setClearingRecent] = useState(false);
  const [clearingSaved, setClearingSaved] = useState(false);
  const [error, setError] = useState(null);
  const [showMealDetails, setShowMealDetails] = useState(false);
  const [selectedMeal, setSelectedMeal] = useState(null);
  const [favorites, setFavorites] = useState([]);
  const [mealServingSizes, setMealServingSizes] = useState({});
  const token = localStorage.getItem("token");

  // Function to load recently viewed meals
  const loadRecentlyViewedMeals = async (showRefreshingState = false) => {
    if (!token) {
      console.log('No token available for loading recently viewed meals');
      return;
    }

    if (showRefreshingState) {
      setRefreshingRecent(true);
    }

    try {
      console.log('=== LOADING RECENTLY VIEWED MEALS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const response = await apiService.apiRequest('/users/recently-viewed-meals');
      console.log('Website API response:', JSON.stringify(response, null, 2));

      const meals = response.recentlyViewedMeals || [];
      console.log('Setting recent meals count:', meals.length);
      console.log('Recent meals:', JSON.stringify(meals.map(m => ({ name: m.name, id: m.id || m._id })), null, 2));

      setRecentMeals(meals);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('Error loading recently viewed meals:', error);
      setRecentMeals([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh recently viewed meals. ';
        if (error.message?.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingRecent(false);
      }
    }
  };

  // Function to load recently added to meal plans
  const loadMealsFromSavedPlans = async (showRefreshingState = false) => {
    if (!token) return;

    if (showRefreshingState) {
      setRefreshingSaved(true);
    }

    try {
      console.log('=== LOADING RECENTLY ADDED TO MEAL PLANS (WEBSITE) ===');
      console.log('Token:', token ? 'exists' : 'missing');

      const response = await apiService.apiRequest('/users/recently-added-to-meal-plans');
      console.log('Website API response:', JSON.stringify(response, null, 2));

      const meals = response.recentlyAddedToMealPlans || [];
      console.log('Setting recently added to meal plans count:', meals.length);
      console.log('Recently added meals:', JSON.stringify(meals.map(m => ({ name: m.name, addedToDate: m.addedToDate, addedToMealType: m.addedToMealType })), null, 2));

      setMealsFromSavedPlans(meals);
      setError(null); // Clear any previous errors
    } catch (error) {
      console.error('❌ Error loading recently added to meal plans:', error);
      setMealsFromSavedPlans([]);

      if (showRefreshingState) {
        let errorMessage = 'Failed to refresh recently added to meal plans. ';
        if (error.message?.includes('Network Error')) {
          errorMessage += 'Please check your internet connection.';
        } else if (error.status === 401) {
          errorMessage += 'Please log in again.';
        } else if (error.status === 500) {
          errorMessage += 'Server error. Please try again later.';
        } else {
          errorMessage += 'Please try again.';
        }
        setError(errorMessage);
      }
    } finally {
      if (showRefreshingState) {
        setRefreshingSaved(false);
      }
    }
  };

  // Function to load all history data
  const loadHistoryData = async () => {
    setLoading(true);
    await Promise.all([
      loadRecentlyViewedMeals(),
      loadMealsFromSavedPlans()
    ]);
    setLoading(false);
  };

  // Load data on mount
  useEffect(() => {
    loadHistoryData();
    loadFavorites();
  }, [token]);

  // Auto-clear error after 5 seconds
  useEffect(() => {
    if (error) {
      const timer = setTimeout(() => {
        setError(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [error]);

  // Clear recently viewed meals
  const clearRecentlyViewed = async () => {
    if (window.confirm('Are you sure you want to clear all recently viewed meals? This action cannot be undone.')) {
      try {
        setClearingRecent(true);
        console.log('🗑️ Starting to clear recently viewed meals...');

        const response = await apiService.apiRequest('/users/recently-viewed-meals', {
          method: 'DELETE'
        });

        console.log('🗑️ Backend response:', response);

        // Clear the frontend state
        setRecentMeals([]);
        setError(null);

        // Also clear localStorage to prevent meals from coming back
        localStorage.removeItem(RECENTLY_VIEWED_KEY);
        localStorage.removeItem('recentlyViewedMeals'); // Alternative key name
        console.log('🗑️ Also cleared localStorage for recently viewed meals');

        console.log('✅ Recently viewed meals cleared successfully');
        alert('Recently viewed meals cleared successfully!');

      } catch (error) {
        console.error('❌ Error clearing recently viewed meals:', error);
        console.error('❌ Error details:', error.response?.data || error.message);
        setError('Failed to clear recently viewed meals: ' + (error.response?.data?.message || error.message));
        alert('Failed to clear recently viewed meals. Please try again.');
      } finally {
        setClearingRecent(false);
      }
    }
  };

  // Clear meals from saved plans
  const clearMealsFromSavedPlans = async () => {
    if (window.confirm('Are you sure you want to clear all meals from saved plans history? This action cannot be undone.')) {
      try {
        setClearingSaved(true);
        console.log('🗑️ Starting to clear recently added to meal plans...');

        const response = await apiService.apiRequest('/users/recently-added-to-meal-plans', {
          method: 'DELETE'
        });

        console.log('🗑️ Backend response:', response);

        // Clear the frontend state
        setMealsFromSavedPlans([]);
        setError(null);

        console.log('✅ Recently added to meal plans cleared successfully');
        alert('Recently added to meal plans history cleared successfully!');

      } catch (error) {
        console.error('❌ Error clearing recently added to meal plans:', error);
        console.error('❌ Error details:', error.response?.data || error.message);
        setError('Failed to clear recently added to meal plans: ' + (error.response?.data?.message || error.message));
        alert('Failed to clear recently added to meal plans. Please try again.');
      } finally {
        setClearingSaved(false);
      }
    }
  };

  // View meal details
  const viewMealDetails = (meal) => {
    setSelectedMeal(meal);
    setShowMealDetails(true);
  };

  // Close meal details
  const closeMealDetails = () => {
    setShowMealDetails(false);
    setSelectedMeal(null);
  };

  // Load favorites from localStorage
  const loadFavorites = () => {
    try {
      const savedFavorites = localStorage.getItem('favoriteMeals');
      if (savedFavorites) {
        setFavorites(JSON.parse(savedFavorites));
      }
    } catch (error) {
      console.error('Error loading favorites:', error);
      setFavorites([]);
    }
  };

  // Check if a meal is favorite
  const isFavorite = (mealId) => {
    return favorites.some(fav => (fav.id || fav._id) === mealId);
  };

  // Toggle favorite status
  const toggleFavorite = (e, meal) => {
    e.stopPropagation();
    const mealId = meal.id || meal._id;

    let updatedFavorites;
    if (isFavorite(mealId)) {
      // Remove from favorites
      updatedFavorites = favorites.filter(fav => (fav.id || fav._id) !== mealId);
    } else {
      // Add to favorites
      updatedFavorites = [...favorites, meal];
    }

    setFavorites(updatedFavorites);
    localStorage.setItem('favoriteMeals', JSON.stringify(updatedFavorites));
  };

  // Add event listener for when user returns to this page
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden) {
        // Page became visible, refresh the data
        loadHistoryData();
      }
    };

    const handleFocus = () => {
      // Window gained focus, refresh the data
      loadHistoryData();
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleFocus);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleFocus);
    };
  }, [token]);

  // Format date for display
  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Format meal type for display
  const formatMealType = (mealType) => {
    return mealType.charAt(0).toUpperCase() + mealType.slice(1);
  };

  // Serving size functions
  const getMealServingSize = (mealId) => {
    return mealServingSizes[mealId] || 1;
  };

  const incrementMealServingSize = (mealId) => {
    setMealServingSizes(prev => ({
      ...prev,
      [mealId]: Math.min((prev[mealId] || 1) + 1, 10) // Max 10 servings
    }));
  };

  const decrementMealServingSize = (mealId) => {
    setMealServingSizes(prev => ({
      ...prev,
      [mealId]: Math.max((prev[mealId] || 1) - 1, 1) // Min 1 serving
    }));
  };

  // Adjust ingredient quantity based on serving size
  const getAdjustedIngredient = (ingredient, mealId) => {
    if (!ingredient) return ingredient;

    const servingSize = getMealServingSize(mealId);
    if (servingSize === 1) return ingredient;

    // Common patterns for ingredient quantities
    const patterns = [
      /^(\d+(?:\.\d+)?)\s*(\w+)?\s+(.+)$/,  // "2 cups flour" or "1.5 tbsp oil"
      /^(\d+(?:\.\d+)?)\s*(.+)$/,           // "2 eggs" or "1.5 onion"
      /^(\d+\/\d+)\s*(\w+)?\s+(.+)$/,       // "1/2 cup sugar"
      /^(\d+)\s*-\s*(\d+)\s*(\w+)?\s+(.+)$/ // "2-3 cloves garlic"
    ];

    for (const pattern of patterns) {
      const match = ingredient.match(pattern);
      if (match) {
        let quantity = match[1];
        let unit = match[2] || '';
        let item = match[3] || match[2] || '';

        // Handle fractions
        if (quantity.includes('/')) {
          const [num, den] = quantity.split('/').map(Number);
          quantity = num / den;
        } else {
          quantity = parseFloat(quantity);
        }

        if (!isNaN(quantity)) {
          const adjustedQuantity = quantity * servingSize;

          // Format the adjusted quantity nicely
          let formattedQuantity;
          if (adjustedQuantity % 1 === 0) {
            formattedQuantity = adjustedQuantity.toString();
          } else if (adjustedQuantity < 1) {
            // Convert to fraction if less than 1
            const fraction = adjustedQuantity;
            if (fraction === 0.5) formattedQuantity = '1/2';
            else if (fraction === 0.25) formattedQuantity = '1/4';
            else if (fraction === 0.75) formattedQuantity = '3/4';
            else if (fraction === 0.33) formattedQuantity = '1/3';
            else if (fraction === 0.67) formattedQuantity = '2/3';
            else formattedQuantity = adjustedQuantity.toFixed(2);
          } else {
            formattedQuantity = adjustedQuantity.toFixed(1).replace('.0', '');
          }

          return `${formattedQuantity}${unit ? ' ' + unit : ''} ${item}`.trim();
        }
      }
    }

    // If no pattern matches, return original ingredient
    return ingredient;
  };

  // Add meal to meal plan
  const addToMealPlan = (meal) => {
    // For now, just show an alert. This could be enhanced to navigate to meal plan page
    alert(`"${meal.name}" will be added to your meal plan. This feature can be enhanced to integrate with the meal planning system.`);
  };

  return (
    <Layout>
      <div className="main-content">
        {/* Error Display */}
        {error && (
          <div style={{
            backgroundColor: '#ffebee',
            color: '#c62828',
            padding: '12px 16px',
            borderRadius: '4px',
            marginBottom: '20px',
            border: '1px solid #ffcdd2',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center'
          }}>
            <span>{error}</span>
            <button
              onClick={() => setError(null)}
              style={{
                background: 'none',
                border: 'none',
                color: '#c62828',
                cursor: 'pointer',
                fontSize: '18px',
                padding: '0 4px'
              }}
            >
              ×
            </button>
          </div>
        )}

        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '20px' }}>
          <h1>Family Meal History</h1>
          <button
            onClick={loadHistoryData}
            disabled={loading}
            style={{
              padding: '10px 20px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '5px',
              cursor: loading ? 'not-allowed' : 'pointer',
              opacity: loading ? 0.6 : 1
            }}
          >
            {loading ? 'Refreshing...' : 'Refresh'}
          </button>
        </div>

          {/* Meals from Saved Plans Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Added to Family Meal Plans</h2>
              <div style={{ display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => loadMealsFromSavedPlans(true)}
                  disabled={refreshingSaved}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: refreshingSaved ? '#ccc' : '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: refreshingSaved ? 'not-allowed' : 'pointer',
                    opacity: refreshingSaved ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {refreshingSaved ? 'Refreshing...' : 'Refresh'}
                </button>
                <button
                  onClick={clearMealsFromSavedPlans}
                  disabled={clearingSaved}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: clearingSaved ? '#ccc' : '#ff4d4f',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: clearingSaved ? 'not-allowed' : 'pointer',
                    opacity: clearingSaved ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {clearingSaved ? 'Clearing...' : 'Clear History'}
                </button>
              </div>
            </div>
            {mealsFromSavedPlans.length === 0 ? (
              <p>No meals from saved plans yet.</p>
            ) : (
              <div className="food-grid">
                {mealsFromSavedPlans.map((meal, index) => (
<div key={`${meal.name}-${index}`} className="food-card meal-plan-card">
  <div className="food-card-image">
    {meal.image ? (
      <img src={meal.image} alt={meal.name} />
    ) : (
      <div className="meal-placeholder">🍽️</div>
    )}
  </div>
  <div className="food-card-content">
    {/* 1. Meal Name */}
    <h3 className="food-card-title">{meal.name}</h3>
    {/* 2. Meta row: Calories, Prep Time, Rating */}
    <div className="food-card-meta">
      {meal.calories && (
        <div className="meta-item calories-tag">
          <span>{meal.calories} cal</span>
        </div>
      )}
      {meal.prepTime && (
        <div className="meta-item prep-time-tag">
          <FaClock /> <span>{meal.prepTime} min</span>
        </div>
      )}
      <div className="meta-item rating">
        <span>{meal.rating && meal.rating > 0 ? meal.rating : 3.5} &#9733;</span>
      </div>
    </div>
    {/* 3. Dietary Tags */}
    {getDietaryBadges(meal).length > 0 && (
      <div className="food-card-tags">
        {getDietaryBadges(meal).map((badge, idx) => (
          <span
            key={idx}
            className="dietary-tag"
            style={{
              background: badge.color,
              color: '#fff',
              display: 'inline-flex',
              alignItems: 'center',
              gap: '0.25em',
            }}
            title={badge.label}
          >
            <span className="dietary-tag-icon">{badge.icon}</span>
            <span className="dietary-tag-label">{badge.label}</span>
          </span>
        ))}
      </div>
    )}
    {/* 4. Description (optional, if you want) */}
    {meal.description && (
      <div className="food-card-description">
        <span>
          {meal.description.length > 80
            ? meal.description.slice(0, 80) + "..."
            : meal.description}
        </span>
      </div>
    )}
    {/* 5. Meal Plan Info */}
    <div className="meal-plan-info">
      <div className="meal-plan-details">
        <span className={`meal-type-badge meal-type-${meal.addedToMealType?.toLowerCase()}`}>
          {formatMealType(meal.addedToMealType)}
        </span>
        <span className="added-date">
          Added {formatDate(meal.addedAt)}
        </span>
      </div>
      <div className="planned-for">
        Planned for: {new Date(meal.addedToDate).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric'
        })}
      </div>
      {meal.planName && (
        <div className="plan-name" style={{
          fontSize: '0.85em',
          color: '#666',
          fontStyle: 'italic',
          marginTop: '4px'
        }}>
          From: {meal.planName}
        </div>
      )}
      {/* View Meal Button centered below plan name */}
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        marginTop: '12px'
      }}>
        <button
          className="view-meal-btn"
          onClick={() => viewMealDetails(meal)}
          style={{
            padding: '8px 16px',
            fontSize: '14px',
            borderRadius: '6px',
            backgroundColor: '#007AFF',
            color: 'white',
            border: 'none',
            cursor: 'pointer',
            transition: 'all 0.2s ease'
          }}
        >
          <span>View Meal</span>
        </button>
      </div>
    </div>
  </div>
</div>
                ))}
              </div>
            )}
          </div>

          {/* Recently Viewed Meals Section */}
          <div className="history-section">
            <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: '10px' }}>
              <h2>Recently Viewed Meals</h2>
              <div style={{ display: 'flex', gap: '10px' }}>
                <button
                  onClick={() => loadRecentlyViewedMeals(true)}
                  disabled={refreshingRecent}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: refreshingRecent ? '#ccc' : '#1890ff',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: refreshingRecent ? 'not-allowed' : 'pointer',
                    opacity: refreshingRecent ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {refreshingRecent ? 'Refreshing...' : 'Refresh'}
                </button>
                <button
                  onClick={clearRecentlyViewed}
                  disabled={clearingRecent}
                  style={{
                    padding: '10px 20px',
                    backgroundColor: clearingRecent ? '#ccc' : '#ff4d4f',
                    color: 'white',
                    border: 'none',
                    borderRadius: '5px',
                    cursor: clearingRecent ? 'not-allowed' : 'pointer',
                    opacity: clearingRecent ? 0.6 : 1,
                    fontSize: '12px',
                    transition: 'all 0.3s ease'
                  }}
                >
                  {clearingRecent ? 'Clearing...' : 'Clear History'}
                </button>
              </div>
            </div>
            {loading ? (
              <p>Loading recently viewed meals...</p>
            ) : recentMeals.length === 0 ? (
              <p>No recently viewed meals yet. Try viewing some meals first!</p>
            ) : (
              <div className="food-grid">
                {recentMeals.map((meal, index) => (
                  <div
                    key={`${meal.name}-${index}`}
                    className="recommendation-card"
                    style={{ animationDelay: `${index * 0.1}s` }}
                  >
                    <div className="recommendation-image">
                      {meal.image ? (
                        <img src={meal.image} alt={meal.name} />
                      ) : (
                        <div className="meal-placeholder">🍽️</div>
                      )}
                      <button
                        className={`favorite-btn ${isFavorite(meal.id || meal._id) ? 'favorited' : ''}`}
                        onClick={(e) => toggleFavorite(e, meal)}
                        title={isFavorite(meal.id || meal._id) ? "Remove from favorites" : "Add to favorites"}
                      >
                        {isFavorite(meal.id || meal._id) ? <FaHeart /> : <FaRegHeart />}
                      </button>
                    </div>
                    <div className="recommendation-content">
                      <h3 className="meal-name">{meal.name}</h3>
                      <div className="meal-meta">
                        <div className="meal-stats">
                          {meal.calories && meal.calories > 0 && (
                            <span className="calories">
                              <FaFire /> {meal.calories} cal
                            </span>
                          )}
                          {meal.prepTime && meal.prepTime > 0 && (
                            <span className="prep-time">
                              <FaClock /> {meal.prepTime}m
                            </span>
                          )}
                        </div>
                        <span className="meal-range-label">
                          {getPesoSigns(meal.priceRange)}
                        </span>
                      </div>
                      {/* Dietary badges */}
                      {getDietaryBadges(meal).length > 0 && (
                        <div className="dietary-badges">
                          {getDietaryBadges(meal).map((badge, idx) => (
                            <span
                              key={idx}
                              className="dietary-badge"
                              style={{
                                backgroundColor: badge.color,
                                color: "#fff",
                                borderRadius: "18px",
                                padding: "2px 8px",
                                fontSize: "0.65em",
                                fontWeight: 600,
                                marginTop: "6px",
                                display: "inline-block",
                                marginRight: "6px"
                              }}
                            >
                              {badge.icon} {badge.label}
                            </span>
                          ))}
                        </div>
                      )}
                      <p className="meal-description">
                        {meal.description?.substring(0, 80)}
                        {meal.description?.length > 80 ? '...' : ''}
                      </p>
                      {/* Ingredients Section - Compact */}
                      {meal.ingredients && meal.ingredients.length > 0 && (
                        <div className="meal-card-ingredients-compact">
                          <div className="ingredients-header">
                            <span className="ingredients-title">Ingredients {getMealServingSize(meal.id || meal._id) > 1 && <span className="serving-note">(for {getMealServingSize(meal.id || meal._id)} servings)</span>}:</span>
                          </div>
                          <div className="ingredients-preview">
                            {meal.ingredients.slice(0, 2).map((ingredient, index) => (
                              <span key={index} className="ingredient-item">
                                {getAdjustedIngredient(ingredient, meal.id || meal._id)}
                                {index < Math.min(meal.ingredients.length, 2) - 1 && ', '}
                              </span>
                            ))}
                            {meal.ingredients.length > 2 && (
                              <button
                                className="view-more-ingredients"
                                onClick={(e) => {
                                  e.stopPropagation();
                                  viewMealDetails(meal);
                                }}
                              >
                                +{meal.ingredients.length - 2} more
                              </button>
                            )}
                          </div>
                        </div>
                      )}

                      {/* Serving Size Controls - Compact */}
                      <div className="serving-size-controls-compact">
                        <span className="serving-label-compact">Servings:</span>
                        <div className="serving-controls-compact">
                          <button
                            className="serving-btn-compact decrement"
                            onClick={(e) => {
                              e.stopPropagation();
                              decrementMealServingSize(meal.id || meal._id);
                            }}
                            disabled={getMealServingSize(meal.id || meal._id) <= 1}
                          >
                            -
                          </button>
                          <span className="serving-count-compact">
                            {getMealServingSize(meal.id || meal._id)}
                          </span>
                          <button
                            className="serving-btn-compact increment"
                            onClick={(e) => {
                              e.stopPropagation();
                              incrementMealServingSize(meal.id || meal._id);
                            }}
                            disabled={getMealServingSize(meal.id || meal._id) >= 4}
                          >
                            +
                          </button>
                        </div>
                      </div>

                      <button
                        className="view-meal-btn"
                        onClick={() => viewMealDetails(meal)}
                      >
                        View Meal <FaChevronRight />
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
      </div>

      {/* Meal Details Modal */}
      {showMealDetails && selectedMeal && (
        <div className="modal-overlay">
          <div className="modal-content">
            <div className="modal-header">
              <h2>{selectedMeal.name}</h2>
              <button className="close-modal" onClick={closeMealDetails}>
                <FaTimes />
              </button>
            </div>
            <div className="modal-body">
              <div className="meal-image">
                {selectedMeal.image ? (
                  <img src={selectedMeal.image} alt={selectedMeal.name} />
                ) : (
                  <div className="meal-placeholder">🍽️</div>
                )}
              </div>
              <div className="meal-details">
                <p className="meal-description">
                  {selectedMeal.description}
                </p>
                <div className="meal-meta">
                  <span className="meal-rating">
                    {selectedMeal.rating && Number(selectedMeal.rating) > 0 ? selectedMeal.rating : 3.5} &#9733;
                  </span>
                  <span className="meal-category">
                    {selectedMeal.category}
                  </span>
                  <span className="meal-price">
                    Calories: {selectedMeal.calories} (
                    {selectedMeal.priceRange} Range)
                  </span>
                </div>
                {selectedMeal.ingredients && selectedMeal.ingredients.length > 0 && (
                  <div className="meal-ingredients">
                    <h3>Ingredients</h3>
                    <ul className="ingredients-list">
                      {selectedMeal.ingredients.map((ingredient, idx) => (
                        <li key={idx}>
                          {ingredient}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
                <div className="meal-nutrition">
                  <h3>Nutrition Information</h3>
                  <div className="nutrition-grid">
                    <div className="nutrition-item">
                      <span className="nutrition-label">Protein:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.protein || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Carbs:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.carbs || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Fat:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.fat || 0)}g
                      </span>
                    </div>
                    <div className="nutrition-item">
                      <span className="nutrition-label">Calories:</span>
                      <span className="nutrition-value">
                        {Math.round(selectedMeal.calories || 0)}
                      </span>
                    </div>
                    {selectedMeal.calcium && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Calcium:</span>
                        <span className="nutrition-value">
                          {selectedMeal.calcium} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.phosphorus && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Phosphorus:</span>
                        <span className="nutrition-value">
                          {selectedMeal.phosphorus} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.iron && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Iron:</span>
                        <span className="nutrition-value">
                          {selectedMeal.iron} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminA && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin A:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminA} µg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminC && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin C:</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminC} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB1 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B1 (Thiamine):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB1} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB2 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B2 (Riboflavin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB2} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.vitaminB3 && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Vitamin B3 (Niacin):</span>
                        <span className="nutrition-value">
                          {selectedMeal.vitaminB3} mg
                        </span>
                      </div>
                    )}
                    {selectedMeal.prepTime && (
                      <div className="nutrition-item">
                        <span className="nutrition-label">Prep Time:</span>
                        <span className="nutrition-value">
                          {selectedMeal.prepTime} mins
                        </span>
                      </div>
                    )}
                  </div>
                </div>
                {selectedMeal.instructions && selectedMeal.instructions.length > 0 && (
                  <div className="meal-steps">
                    <h3>Cooking Instructions</h3>
                    <ol>
                      {selectedMeal.instructions.map((step, idx) => (
                        <li key={idx}>{step}</li>
                      ))}
                    </ol>
                  </div>
                )}
                {selectedMeal.dietaryTags && selectedMeal.dietaryTags.length > 0 && (
                  <div className="meal-tags">
                    <h3>Dietary Tags</h3>
                    <div className="tags-container">
                      {selectedMeal.dietaryTags.map((tag, idx) => (
                        <span key={idx} className="dietary-tag">
                          {tag}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
                {selectedMeal.mealType && selectedMeal.mealType.length > 0 && (
                  <div className="meal-types">
                    <h3>Meal Types</h3>
                    <div className="tags-container">
                      {selectedMeal.mealType.map((type, idx) => (
                        <span key={idx} className="meal-type-tag">
                          {type}
                        </span>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="modal-footer">
              <button
                className="favorite-modal-btn"
                onClick={(e) => toggleFavorite(e, selectedMeal)}
              >
                {isFavorite(selectedMeal.id || selectedMeal._id) ? (
                  <>
                    Remove from Favorites <FaHeart />
                  </>
                ) : (
                  <>
                    Add to Favorites <FaRegHeart />
                  </>
                )}
              </button>
            </div>
          </div>
        </div>
      )}
    </Layout>
  );
};

export default History;